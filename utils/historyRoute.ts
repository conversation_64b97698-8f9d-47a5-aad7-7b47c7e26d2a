// hooks/watchUrlChange.ts
export function watchUrlChange(callback: (url: string) => void) {
  let lastUrl = location.href;

  const checkUrlChange = () => {
    const currentUrl = location.href;
    if (currentUrl !== lastUrl) {
      lastUrl = currentUrl;
      callback(currentUrl);
    }
  };

  // 防止重复 patch
  if (!(window as any).__hasHookedHistory__) {
    const rawPushState = history.pushState;
    const rawReplaceState = history.replaceState;

    history.pushState = function (...args) {
      rawPushState.apply(this, args);
      window.dispatchEvent(new Event("urlchange"));
    };

    history.replaceState = function (...args) {
      rawReplaceState.apply(this, args);
      window.dispatchEvent(new Event("urlchange"));
    };

    (window as any).__hasHookedHistory__ = true;
  }

  window.addEventListener("popstate", checkUrlChange);
  window.addEventListener("urlchange", checkUrlChange);

  // 兜底轮询（某些异常情况）
  const interval = setInterval(checkUrlChange, 500);

  // 初始执行一次
  checkUrlChange();

  // 返回取消函数（可选）
  return () => {
    window.removeEventListener("popstate", checkUrlChange);
    window.removeEventListener("urlchange", checkUrlChange);
    clearInterval(interval);
  };
}
