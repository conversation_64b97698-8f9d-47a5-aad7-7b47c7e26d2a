import React, { useState } from "react";
import { Button, Checkbox, Flex, Tag, theme } from "antd";
import { FileOutlined, SaveOutlined } from "@ant-design/icons";
import "./index.less";
import IconFont from "@/components/IconFont";

type Props = {
  data: {
    id: string;
    libName: string;
    docCount: number;
    allFileSize: string;
    isCollect?: string;
    createBy?: string;
    createBy_text?: string;
  };
  knowledgeType?: string; // 当前tab栏切换 1最近 3企业 2 项目  4 个人
  checked?: boolean;
  currentUserInfo?: any;
  onCheckChange?: (checked: boolean, value: any, type: string) => void;
};
const { useToken } = theme;
const KnowledgeCard: React.FC<Props> = ({ data, knowledgeType, checked = false, onCheckChange, currentUserInfo }) => {
  const { token } = useToken();
  return (
    <Flex className="knowledge-card-chat" vertical gap={token.marginXXS}>
      <Flex justify="space-between">
        <Flex align="center" gap={token.marginXXS}>
          <Button
            className="btn-knowledge-icon"
            type="text"
            size="small"
            style={
              knowledgeType === "2"
                ? { backgroundColor: token.volcano1 }
                : knowledgeType === "3"
                  ? { backgroundColor: token.geekblue1 }
                  : knowledgeType === "4"
                    ? { backgroundColor: token.cyan1 }
                    : {}
            }
            icon={
              <IconFont
                className="icon"
                type="knowledgeBaseOutlined"
                fill={
                  knowledgeType == "2"
                    ? token.volcano6
                    : knowledgeType == "3"
                      ? token.geekblue6
                      : knowledgeType == "4"
                        ? token.cyan6
                        : undefined
                }
              />
            }
          />
          {data?.isCollect == "1" && (
            <Tag color="#FFFBE6" style={{ color: "#FAAD14" }}>
              常用
            </Tag>
          )}
        </Flex>
        <Checkbox
          value={data.id}
          style={{
            opacity: checked ? 1 : 0,
            transition: "opacity 0.2s",
          }}
          className="knowledge-checkbox"
          checked={checked}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) => onCheckChange?.(e.target.checked, data, "1")}
        />
      </Flex>

      <Flex className="knowledge-name">{data.libName}</Flex>

      <Flex gap={8} align="center" className="knowledge-card-opeate">
        <Flex
          className="knowledge-card-info"
          style={{
            color: token.colorTextQuaternary,
            fontSize: token.fontSizeSM,
            lineHeight: "24px",
          }}
          align="center"
        >
          {knowledgeType == "2" && data?.createBy && data?.createBy_text && (
            <Tag
              bordered={false}
              style={{ borderRadius: "50%", marginRight: "4px", color: token.colorTextTertiary, fontSize: "10px" }}
            >
              {currentUserInfo?.id == data?.createBy ? "我" : data?.createBy_text?.charAt(0)}
            </Tag>
          )}
          <FileOutlined />
          <span style={{ marginLeft: "2px", marginRight: token.marginXXS }}>{data.docCount || 0}</span>
          <SaveOutlined style={{ marginLeft: token.marginXXS }} />
          <span style={{ marginLeft: "2px" }}>{(data?.allFileSize ?? 0) + "MB"}</span>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default KnowledgeCard;
