import {
  Flex,
  message,
  Spin,
  theme,
  Button,
  TabsProps,
  Segmented,
  Switch,
  Popover,
  Breadcrumb,
  Typography,
  Input,
  Empty,
  Modal,
  Form,
  Select,
  Tag,
  Col,
  Cascader,
  CascaderProps,
  Divider,
  Popconfirm,
  Upload,
  UploadProps,
  Tooltip,
} from "antd"; // 引入 Spin 组件
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import IconFont from "@/components/IconFont";
import { getToken, getTenantId, getUserInfo } from "@/utils/auth.ts";
import "./index.less";
import TopTitle from "../../components/TopTitle";
import {
  CloseCircleOutlined,
  DeleteOutlined,
  DesktopOutlined,
  FileOutlined,
  InfoCircleTwoTone,
  PlusOutlined,
  SaveOutlined,
  SearchOutlined,
  SelectOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import KnowledgeCard from "./components/knowledgeCard";
import LearnCard from "./components/learnCard";
import ClassCard from "./components/classCard";
const { useToken } = theme;
const { TextArea } = Input;
const { CheckableTag } = Tag;
const Knowledge: React.FC<{ toChat }> = ({ toChat }) => {
  const [form] = Form.useForm();
  const [transferForm] = Form.useForm();
  const [shareModalForm] = Form.useForm(); // 分享知识库弹框
  const searchParamsInit = {
    pageNum: 1,
    pageSize: 300000,
    entity: {
      libName: "",
    },
  };
  const fetchRequest = useFetchRequest();
  const [knowHome, setKnowHome] = useState<string>("知识库"); // 头部信息
  const { token } = useToken();
  const [currentUserInfo, setCurrentUserInfo] = useState<any>({}); // 当前用户信息
  const [alignValue, setAlignValue] = React.useState<string>("1"); // tabs 默认数据
  const [isLearnType, setIsLearnType] = React.useState<string>("2"); // 项目库2 企业库3 个人库是4
  // const [isSearch, setIsSearch] = useState<boolean>(false); // 是否是搜索页面
  const isSearch = useRef(false);
  const [searchParams, setSearchParams] = useState(searchParamsInit); // 搜索参数
  const [searchValue, setSearchValue] = useState<string>(""); // 搜索框值
  const [searchWebValue, setSearchWebValue] = useState<string>(""); // 网页知识搜索框值
  const [addOredit, setAddOredit] = useState<string>("add"); // 知识库弹框 新建 / 编辑
  const [modal2Open, setModal2Open] = useState(false); // 知识库弹框是否打开
  const [pageLoading, setPageLoading] = useState<boolean>(false); // 页面整体的loading
  const [loading, setLoading] = useState<boolean>(false); // 新增按钮保存等loading
  const [cardData, setCardData] = useState<any>([]); // 知识库card数据
  const [webUrlCardData, setWebUrlCardData] = useState<any>([]); // 网页知识库的数据
  const [selectedTags, setSelectedTags] = useState<string>("today"); // 网页知识tag
  const [visible, setVisible] = useState(false); // 转移弹框是否展示
  const [isShareModal, setIsShareModal] = useState(false); // 分享弹框是否展示
  const [showPage, setShowPage] = useState("homePage"); //当前展示的页面 // webUrlPage 是网页知识库页面 // knowledgePage 知识页面 // homePage 主页 // searchPage  搜索页面
  const [webList, setWebList] = useState([]); // 网页知识库数据
  const [breadcrumbItems, setBreadcrumbItems] = useState<any>([]); // nav导航的数据 面包屑
  const [navigationStack, setNavigationStack] = useState<any[]>([]); // 导航栈，用于记录导航路径
  const [checkedIds, setCheckedIds] = useState<string[]>([]); // 知识库，知识选中的数据id
  const [checkedData, setCheckedData] = useState<string[]>([]); // 知识库，知识选中的数据
  const [currentClassId, setCurrentClassId] = useState<any>(""); // 企业库当前选中的分类id
  const [currentId, setCurrentId] = useState<any>(""); // 当前选中的知识库id
  const [userList, setUserList] = useState<any>([]); // 获取当前所有的用户信息
  const [knowledgeList, setKnowledgeList] = useState<any>([]); // 获取当前当前分类下的知识列表
  const [searchData, setSearchData] = useState<any>({}); // 搜索下的数据 enterpriseQueryVO 企业库  teamQueryVO 个人库 projectQueryVO 项目库
  const [selectUser, setSelectUser] = useState<any>([]); // 分享知识库选中的用户
  const [transferList, setTransferList] = useState<any>([]); // 弹框转移知识库列表
  const [editLoading, setEditLoading] = useState<boolean>(false); // 弹框的loading
  const [transferKnowData, setTransferKnowData] = useState<any>({}); // 单个转移的知识库数据
  const [currentKnowData, setCurrentKnowData] = useState<any>({}); // 点击编辑，拿到的当前用户想要编辑的知识库信息
  const [dataLength, setDataLength] = useState<number>(0); // 页面数据的数量
  const [classData, setClassData] = useState<any>([]); // 知识库分类数据 用于返回时做处理
  const [defultCommon, setDefultCommon] = useState<any>({ id: "0-0", name: "常用企业知识库", parentId: "0" }); // 默认的常用知识库
  const [expandedMap, setExpandedMap] = useState<Record<string, boolean>>({}); // 搜索查看更多
  const [selectType, setSelectType] = useState<string>(""); // 当前多选选中的是知识库还是知识 1是知识库  2是知识，3是网页知识(暂时没用到)
  const [info, setInfo] = useState<any>({ defaultStorage: false, defaultStorageLibId: "" }); // 默认配置信息
  const [webVisible, setWebVisible] = useState(false); // 网页知识自动采集 弹框 是否展示
  const [list, setList] = useState<any>([]); // 网页知识库存入知识库下拉框数据
  // 搜索点击查看更多
  const toggleExpand = (key: string) => {
    setExpandedMap((prev) => ({ ...prev, [key]: true }));
  };

  const optionsList: TabsProps["items"] = [
    { key: "1", label: "最近" },
    { key: "2", label: "项目" },
    { key: "3", label: "企业" },
    { key: "4", label: "个人" },
  ];
  const typeMap = {
    // 搜索key 对应的类型
    enterpriseQueryVO: 3, // 企业
    projectQueryVO: 2, // 项目
    teamQueryVO: 4, // 个人
  };
  // 网页知识tag
  const tagsData = [
    { label: "今天", value: "today" },
    { label: "昨天", value: "yesterday" },
    { label: "本周", value: "thisWeek" },
    { label: "本月", value: "thisMonth" },
    { label: "全部", value: "all" },
  ];

  // 构建面包屑导航
  const buildBreadcrumb = (navigationPath: any[]) => {
    if (isSearch.current) {
      return;
    }
    const items = navigationPath.map((nav, index) => ({
      title: (
        <span style={{ cursor: "pointer" }} onClick={() => navigateToLevel(index)}>
          {nav.title}
        </span>
      ),
      key: nav.id || index,
    }));
    setBreadcrumbItems(items);
  };

  // 导航到指定层级
  const navigateToLevel = (targetIndex: number) => {
    const targetNav = navigationStack[targetIndex];
    if (!targetNav) {
      if (targetIndex > 0) {
        return;
      }
      setShowPage("homePage");
      setKnowHome("知识库");
      setBreadcrumbItems([]);
      setClassData([]);
      setCardData([]);
      if (alignValue == "3") {
        setCurrentClassId("");
      } else {
        getRecentList(alignValue);
      }
      // setCurrentClassId("");
      // getRecentList(alignValue);
      setNavigationStack([]); // 清空导航栈
      return;
    }

    // 更新导航栈，保留到目标层级
    const newStack = navigationStack.slice(0, targetIndex + 1);
    setNavigationStack(newStack);

    // 清空选中状态
    setCheckedIds([]);
    setCheckedData([]);

    // 根据目标层级执行相应操作
    if (targetIndex === 0) {
      // 返回到根级别（知识库首页）
      if (alignValue === "3") {
        setCurrentClassId(""); // 清空会自动调用接口
      } else {
        getRecentList(alignValue);
      }
      setShowPage("homePage");
      setKnowHome("知识库");
      setBreadcrumbItems([]);
      setClassData([]);
      setNavigationStack([]); // 清空导航栈
      // 调用对应接口刷新数据
    } else {
      // 返回到指定分类层级
      setShowPage("homePage");
      setKnowHome(targetNav.title);
      if (alignValue === "3") {
        if (targetNav.id == currentClassId) {
          getRecentList(alignValue, targetNav.id);
        } else {
          setCurrentClassId(targetNav.id);
        }
      } else {
        getRecentList(alignValue, targetNav.id);
      }

      // 重新构建面包屑
      buildBreadcrumb(newStack);
    }
  };

  // 头部点击返回
  const handleBack = () => {
    if (isSearch.current) {
      debounceSearch();
      setShowPage("searchPage");
      // setCurrentClassId("");
      setKnowHome("知识库");
    } else {
      if (showPage == "webUrlPage") {
        // 从网页知识库页面返回
        setShowPage("homePage");
        setKnowHome("知识库");
        setCurrentClassId("");
        setBreadcrumbItems([]);
        setNavigationStack([]);
        getRecentList(alignValue);
      } else if (showPage == "knowledgePage") {
        // 从知识库详情页面返回
        if (navigationStack.length > 1) {
          // 返回到上一级分类
          navigateToLevel(navigationStack.length - 2);
        } else {
          // 返回到根级别
          navigateToLevel(0);
        }
      } else {
        // 其他页面使用导航栈进行返回
        if (navigationStack.length > 1) {
          // 返回到上一级
          navigateToLevel(navigationStack.length - 2);
        } else {
          // 返回到根级别
          navigateToLevel(0);
        }
      }
    }
    setCheckedIds([]);
    setCheckedData([]); // 清空选中的数据
  };
  // 搜素框change
  const inputChange = (e) => {
    const value = e.target.value.trim();
    setSearchValue(value);
    // 不是输入法状态才搜索
    if (!latestRequestIdRef.current) {
      debounceSearch();
    }
  };
  const latestRequestIdRef = useRef(false); // 请求标识
  const searchRef = useRef(() => {});

  useEffect(() => {
    searchRef.current = () => {
      setPageLoading(true);
      fetchRequest({
        api: "baseGetList",
        params: {
          libName: searchValue,
        },
        callback: (res) => {
          if (res.code === 200) {
            setSearchData(res?.data || {});
          } else {
            message.open({
              type: "error",
              content: "获取失败",
            });
          }
          setPageLoading(false);
        },
      });
    };
  }, [searchValue]);
  // debounce 永远不变，内部调用 ref 获取最新逻辑  网页知识库列表接口
  const debounceSearch = useMemo(() => {
    return debounce(() => {
      searchRef.current();
    }, 600);
  }, []);

  const handleCompositionStart = () => {
    latestRequestIdRef.current = true;
  };

  const handleCompositionEnd = (e) => {
    latestRequestIdRef.current = false;
    setSearchValue(e.target.value);
    debounceSearch();
  };

  // 新建知识库按钮点击
  const createKnowledge = () => {
    setAddOredit("add");
    setModal2Open(true);
    form.resetFields();
  };

  // 新建/编辑知识库
  const handlerSubmit = async () => {
    try {
      form.setFieldValue("libType", "TEXT_LIBRARY");
      await form.validateFields();
      const formValues = form.getFieldsValue();
      setLoading(true);
      // 提交逻辑这里 个人库
      if (addOredit === "edit") {
        let api = "editKnowledge";
        let obj: any = {
          ...currentKnowData,
          libId: currentId,
          libName: formValues.libName,
          libDesc: formValues.libDesc || "",
        };
        // 说明是项目库
        if (alignValue == "2") {
          api = "editKnowledgeProj";
          obj = {
            ...obj,
            userList: selectUser,
            permissionRange: selectUser.length > 0 ? "permission_range_designate" : "permission_range_private",
          };
        }
        fetchRequest({
          api: api,
          params: obj,
          callback: (res) => {
            if (res.code === 200) {
              message.open({
                type: "success",
                content: "修改成功",
              });
              setModal2Open(false); // 关闭模态框
              if (showPage == "searchPage") {
                debounceSearch();
              } else {
                getRecentList(alignValue);
              }
              form.resetFields();
            } else {
              message.open({
                type: "error",
                content: res.msg,
              });
            }
            setLoading(false);
          },
        });
      } else {
        let api = "addKnowledge"; // 个人库
        let obj: any = {
          libName: formValues.libName,
          libType: "TEXT_LIBRARY",
          libDesc: formValues.libDesc || "",
        };
        // 说明是项目库
        if (alignValue == "2") {
          api = "addBaseProject";
          obj = {
            ...obj,
            userList: selectUser,
            permissionRange: "permission_range_designate",
          };
        }
        fetchRequest({
          api: api,
          params: obj,
          callback: (res) => {
            if (res.code === 200) {
              message.open({
                type: "success",
                content: "添加成功",
              });
              setModal2Open(false); // 关闭模态框
              getRecentList(alignValue);
              form.resetFields();
            } else {
              message.open({
                type: "error",
                content: res.msg,
              });
            }
            setLoading(false);
          },
        });
      }
    } catch (error) {
      console.error("Validation Failed:", error);
    }
  };
  // 转移取消
  const onCancel = () => {
    setVisible(false);
    setEditLoading(false);
    transferForm.resetFields();
  };
  // 当用户点击个单个数据进行了操作时，如果多选里面有值需要去掉当前单个的数据, 否则则是多选
  const onSingleClick = () => {
    if (transferKnowData?.id) {
      // 同步移除 checkedData 和 checkedIds 中的对应数据
      setCheckedData((prev) => prev.filter((item: any) => item.id !== transferKnowData.id));
      setCheckedIds((prev) => prev.filter((id) => id !== transferKnowData.id));
    } else {
      setCheckedData([]);
      setCheckedIds([]);
    }
    setTransferKnowData({});
  };
  // 转移确定
  const onSubmit = (type) => {
    let arr = [];
    if (transferKnowData?.id) {
      // 如果单个数据有值，说明是单个转移，如果是批量转移这个数据会清空
      let obj = {
        libId: transferForm.getFieldValue("libId"),
        docId: transferKnowData.id,
        curLibId: currentId,
        title: transferKnowData?.title,
        url: transferKnowData?.url,
        type: transferKnowData?.url ? "EXCEL" : "OWNER", // 网页转个人：EXCEL，个人转个人：OWNER
      };
      arr.push(obj);
    } else {
      // 批量转移
      if (checkedData.length > 0) {
        const batchArr = checkedData.map((item: any) => ({
          libId: transferForm.getFieldValue("libId"),
          docId: item.id,
          curLibId: currentId,
          title: item.title,
          url: item.url,
          type: item.url ? "EXCEL" : "OWNER",
        }));
        arr = arr.concat(batchArr);
      }
    }
    setLoading(true);
    fetchRequest({
      api: "transferDoc",
      params: arr,
      callback: (res) => {
        setLoading(false);
        setEditLoading(false);
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "转移成功",
          });
          // 单个转移后，如果多选里面有也需要去掉, 批量也需要去掉
          onSingleClick();
          onCancel();
          debounceKnowSearch();
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  // 点击分享知识库弹框确定
  const onShareSubmit = () => {
    setLoading(true);
    fetchRequest({
      api: "memberShare",
      params: {
        libId: currentId,
        userList: selectUser,
        permissionRange: selectUser.length > 0 ? "permission_range_designate" : "permission_range_private",
      },
      callback: (res) => {
        setLoading(false);
        if (res.code == 200) {
          setIsShareModal(false);
          message.open({
            type: "success",
            content: "分享成功！",
          });
          if (showPage == "searchPage") {
            debounceSearch();
          } else {
            getRecentList(alignValue);
          }
        } else {
          message.open({
            type: "error",
            content: "分享失败！",
          });
        }
      },
    });
  };
  // 网页知识tag
  const handleChange = (tag: any) => {
    if (selectedTags !== tag.value) {
      setSelectedTags(tag.value); // 只有不是当前值时才切换
      debounceKnowSearch();
    }
  };

  // 搜素框 网页列表change
  const inputWebChange = (e) => {
    const value = e.target.value.trim();
    setSearchWebValue(value);
    // 不是输入法状态才搜索
    if (!latestRequestIdRef.current) {
      debounceKnowSearch();
    }
  };

  const getJumpUrlRef = useRef(() => {});
  const latestUrlRef = useRef(false); // 请求标识
  useEffect(() => {
    setKnowledgeList([]);
    setWebList([]);
    setDataLength(0);
    getJumpUrlRef.current = () => {
      setPageLoading(true);
      fetchRequest({
        api: "getItemDetail",
        params: {
          pageNum: 1,
          pageSize: 300000,
          entity: {
            title: searchWebValue,
            baseId: currentId,
            timeRange: currentId == webUrlCardData.id ? selectedTags : "",
          },
        },
        callback: (res) => {
          if (res.code === 200) {
            if (currentId != webUrlCardData.id) {
              setKnowledgeList(res?.data.records || []);
              setDataLength(res?.data.total || 0);
            } else {
              // 网页知识库
              setWebList(res?.data?.records || []);
              setDataLength(res?.data?.total || 0);
            }
          } else {
            message.open({
              type: "error",
              content: "获取失败",
            });
          }
          setPageLoading(false);
        },
      });
    };
  }, [searchWebValue, selectedTags, currentId]);
  const handleWebStart = () => {
    latestUrlRef.current = true;
  };

  const handleWebEnd = (e) => {
    latestUrlRef.current = false;
    setSearchWebValue(e.target.value);
    debounceSearch();
  };
  // debounce 永远不变，内部调用 ref 获取最新逻辑  网页知识库列表接口
  const debounceKnowSearch = useMemo(() => {
    return debounce(() => {
      getJumpUrlRef.current();
    }, 600);
  }, []);

  // 添加当前网页
  const addInterItem = () => {
    const links = document.getElementsByTagName("link");
    let favicon = null;
    for (let i = 0; i < links.length; i++) {
      if (links[i].rel.includes("icon")) {
        favicon = links[i].href;
        break;
      }
    }
    setPageLoading(true);
    fetchRequest({
      api: "clientCrawSave",
      params: {
        title: document.title,
        icon: favicon,
        url: window.location.href,
        libId: webUrlCardData?.id,
      },
      callback: async (res) => {
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "添加网页成功！",
          });
          debounceKnowSearch();
        } else {
          message.open({
            type: "error",
            content: "添加网页失败！",
          });
          setPageLoading(false);
        }
      },
    });
  };
  // 用户选中的数据
  const handleCheckChange = (checked: boolean, data: any, type: string) => {
    setSelectType(type);
    setCheckedIds((prev) => {
      if (checked) {
        return [...prev, data?.id];
      } else {
        return prev.filter((itemId) => itemId !== data?.id);
      }
    });
    setCheckedData((prev) => {
      if (checked) {
        const newData = { ...data, isTypeKnow: type }; // 1 是知识库 2是知识
        return [...prev, newData];
      } else {
        return prev.filter((item: any) => item?.id !== data?.id);
      }
    });
  };
  // 删除知识库
  const handleKnowledgeDelete = (item, type?) => {
    let arr = [];
    if (type == 1) {
      setTransferKnowData(item); // 当前要转移的数据单个的,如果删除成功需要去掉的
      // 单个删除
      arr.push(item.id);
    } else {
      // 批量删除
      arr = checkedIds;
    }
    setPageLoading(true);
    fetchRequest({
      api: "delTeamBatch",
      params: arr,
      callback: async (res) => {
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "删除成功！",
          });
          onSingleClick();
          if (showPage == "searchPage") {
            debounceSearch();
          } else {
            getRecentList(alignValue);
          }
        } else {
          message.open({
            type: "error",
            content: "删除失败！",
          });
          setPageLoading(false);
        }
      },
    });
  };
  // 删除网页
  const handleDelete = async (item, type?) => {
    let arr = [];
    if (type == 1) {
      setTransferKnowData(item); // 当前要转移的数据单个的,如果删除成功需要去掉的
      // 单个删除
      arr.push(item.id);
    } else {
      // 批量删除
      arr = checkedIds;
    }
    setPageLoading(true);
    fetchRequest({
      api: "delDocBatch",
      params: arr,
      callback: async (res) => {
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "删除成功！",
          });
          onSingleClick();
          if (showPage == "searchPage") {
            // 如果是在列表页面 调用搜索接口
            debounceSearch();
          } else if (alignValue == "1") {
            // 如果是在最近，则调用最近接口
            getRecentList(alignValue);
          } else {
            debounceKnowSearch();
          }
        } else {
          message.open({
            type: "error",
            content: "删除失败！",
          });
          setPageLoading(false);
        }
      },
    });
  };
  // 转移 只能转移到个人库下面
  const handleTransfer = (data?) => {
    setVisible(true);
    setEditLoading(true);
    if (data) {
      setTransferKnowData(data); // 当前要转移的数据单个的
    }
    const paramsObj = { ...searchParams };
    // 获取个人库列表
    fetchRequest({
      api: "getTeamListBy",
      params: paramsObj,
      callback: (res) => {
        if (res.code == 200) {
          // 过滤网页知识库 跟当前库
          const filteredList = (res?.data?.records || []).filter(
            (item) => item.isWebLib != "1" && item.id !== currentId,
          );
          setTransferList(filteredList);
        } else {
          message.open({
            type: "error",
            content: "获取失败",
          });
        }
        setEditLoading(false);
      },
    });
  };

  // 获取最近的知识库
  const getRecentList = (type, classId?) => {
    setPageLoading(true);
    let apiUrl = "recentList";
    let paramsObj = {};
    if (type == "1") {
      apiUrl = "recentList"; // 最近
    } else if (type == "2") {
      apiUrl = "getProjectList"; // 项目
      paramsObj = { ...searchParams };
    } else if (type == "3") {
      apiUrl = "getListByCategory"; // 企业
      paramsObj = { parentDirId: classId ? classId : currentClassId && currentClassId != "0-0" ? currentClassId : 0 };
    } else if (type == "4") {
      apiUrl = "getTeamListBy"; // 个人
      paramsObj = { ...searchParams };
    }
    if (currentClassId == "0-0") {
      getCommonData();
      return;
    }
    fetchRequest({
      api: apiUrl,
      params: paramsObj,
      callback: (res) => {
        if (res.code === 200) {
          if (type == "3") {
            setCardData(res?.data?.knowledgeLibBaseVOList || []);
            setClassData(res?.data?.knowledgeLibDirVOList || []);
          } else {
            if (type == "1") {
              // 最近
              setCardData(res?.data || []);
            } else {
              if (type == "4") {
                // 个人库过滤网页知识库
                const dataList = res?.data?.records || [];
                const urlCardData = dataList.find((item) => item.isWebLib === "1");
                const filteredRecords = dataList.filter((item) => item.isWebLib != "1");
                setCardData(filteredRecords || []);
                setWebUrlCardData(urlCardData || []); // 网页知识库的数据
              } else {
                // 其他
                setCardData(res?.data?.records || []);
              }
            }
          }
        } else {
          setCardData([]);
          setClassData([]);
          message.open({
            type: "error",
            content: "获取失败",
          });
        }
        setPageLoading(false);
      },
    });
  };
  useEffect(() => {
    // 获取知识库
    if (currentClassId == "0-0") {
      getCommonData();
    } else {
      setCardData([]);
      getRecentList(alignValue);
    }
  }, [alignValue, currentClassId]);

  // 点击跳转到知识页面 或者企业知识库页面   navName为面包屑的name  titleName为头部的名字
  const getKnowDetail = (item, titleName) => {
    const baseBreadcrumb = {
      2: "项目库",
      3: "企业库",
      4: "个人库",
    };

    setKnowHome(titleName || "知识库");
    if (isSearch.current) {
      return;
    }
    if (alignValue !== "3") {
      // 项目库 or 个人库 - 简单的两级导航
      const newStack = [
        { id: "root", title: baseBreadcrumb[alignValue] },
        { id: item.id, title: titleName },
      ];
      setNavigationStack(newStack);
      buildBreadcrumb(newStack);
      return;
    }

    // 企业库 - 支持多级导航
    setCardData([]);

    if (item?.parentId === "0" || item?.id === "0-0") {
      // 一级分类（根分类）
      const newStack = [
        { id: "root", title: "企业库" },
        { id: item.id, title: titleName, parentId: item.parentId },
      ];
      setNavigationStack(newStack);
      buildBreadcrumb(newStack);
    } else if (item?.parentId) {
      // 多级分类 - 添加到现有导航栈
      const newNav = { id: item.id, title: titleName, parentId: item.parentId };

      // 检查是否已存在相同层级的导航
      const existingIndex = navigationStack.findIndex((nav) => nav.id === item.id);

      if (existingIndex >= 0) {
        // 如果已存在，截断到该层级
        const newStack = navigationStack.slice(0, existingIndex + 1);
        setNavigationStack(newStack);
        buildBreadcrumb(newStack);
      } else {
        // 添加新的导航层级
        const newStack = [...navigationStack, newNav];
        setNavigationStack(newStack);
        buildBreadcrumb(newStack);
      }
    } else {
      // 最终层级（知识库详情页面）- 不可点击的面包屑项
      const newStack = [...navigationStack, { id: item.id, title: titleName, isLeaf: true }];
      setNavigationStack(newStack);

      // 构建面包屑，最后一项不可点击
      const items = newStack.map((nav, index) => ({
        title:
          index === newStack.length - 1 ? (
            nav.title
          ) : (
            <span style={{ cursor: "pointer" }} onClick={() => navigateToLevel(index)}>
              {nav.title}
            </span>
          ),
        key: nav.id || index,
      }));
      setBreadcrumbItems(items);
    }
  };

  // 企业库分类点击
  const onClassClick = (item) => {
    setCheckedIds([]); // 清空选中的数据
    setCheckedData([]); // 清空选中的数据
    setCurrentClassId(item.id);
    getKnowDetail(item, item.name);
  };
  // 获取所有用户信息
  const getUserList = () => {
    fetchRequest({
      api: "getBaseEmployee",
      params: {},
      callback: (res) => {
        if (res.code === 200) {
          setUserList(res?.data || []);
        }
      },
    });
  };
  //上传前提
  const uploadProps: UploadProps = {
    name: "file",
    multiple: true,
    showUploadList: false,
    beforeUpload(file) {
      if (file.size <= 0) {
        message.error("上传文件的大小必须大于0！");
        return false;
      }
    },
  };

  // 文件转base64
  function fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      // 成功读取文件时的回调
      reader.onload = () => {
        resolve(reader.result); // Base64 编码的字符串
      };

      // 读取文件失败时的回调
      reader.onerror = (error) => {
        reject(error);
      };

      // 读取文件并转为 Base64
      reader.readAsDataURL(file);
    });
  }

  // 上传文件
  const handleCustomRequest = async (options) => {
    setPageLoading(true);
    const { file } = options;
    const formData = {
      fileStr: await fileToBase64(file),
      fileName: file.name,
      baseId: currentId,
    };
    fetchRequest({
      api: "uploadKnowledgeFile",
      params: formData,
      file: true,
      callback: async (res) => {
        setPageLoading(false);
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "上传成功",
          });
          debounceKnowSearch(); // 刷新列表
        } else {
          message.open({
            type: "error",
            content: "上传失败",
          });
        }
      },
    });
  };
  // 递归查找每个 id 对应的节点 分享人员
  const findLabelsByIds = (options, ids) => {
    const result = [];
    const traverse = (nodes) => {
      for (const node of nodes) {
        if (ids.includes(node.userId)) {
          result.push({ userId: node.userId, realName: node.realName });
        }
        if (node.children) {
          traverse(node.children);
        }
      }
    };

    traverse(options);
    return result;
  };
  // 知识库卡片点击编辑
  const handkeEdit = (data) => {
    setCurrentId(data.id);
    setEditLoading(true);
    setCurrentKnowData(data);
    fetchRequest({
      api: "memberList",
      params: {
        id: data.id,
      },
      callback: (res) => {
        if (res.code == 200) {
          let arr = [];
          let info = [];
          res.data.map((item) => {
            if (item.adminFlag != 1) {
              arr.push([item.userId]);
              info.push({ userId: item.userId, realName: item.realName });
            }
          });
          setSelectUser(info);
          form.setFieldsValue({
            libName: data.libName,
            libDesc: data.libDesc,
            userInfo: arr,
          });
        }
        setEditLoading(false);
      },
    });
    setModal2Open(true);
    setAddOredit("edit");
  };
  // 知识库卡片点击分享
  const handkeShare = (data) => {
    setCurrentId(data?.id);
    setEditLoading(true);
    setIsShareModal(true);
    fetchRequest({
      api: "memberList",
      params: {
        id: data.id,
      },
      callback: (res) => {
        if (res.code == 200) {
          let arr = [];
          let info = [];
          res.data.map((item) => {
            if (item.adminFlag != 1) {
              arr.push([item.userId]);
              info.push({ userId: item.userId, realName: item.realName });
            }
          });
          setSelectUser(info);
          shareModalForm.setFieldsValue({
            userInfo: arr,
          });
        }
        setEditLoading(false);
      },
    });
  };

  // 查询常用知识库列表
  const getCommonData = () => {
    setPageLoading(true);
    setClassData([]);
    fetchRequest({
      api: "getBaseCommonList",
      params: {
        pageNum: 1,
        pageSize: 300000,
        entity: {
          libName: "",
        },
      },
      callback: (res) => {
        if (res.code == 200) {
          setCardData(res?.data?.records || []);
        }
        setPageLoading(false);
      },
    });
  };
  // 因为刚进入页面需要获取个人库列表 才能配置 网页存入到哪个知识库
  const getSelectData = () => {
    setWebVisible(true);
    setEditLoading(true);
    fetchRequest({
      api: "getTeamListBy",
      params: {
        pageNum: 1,
        pageSize: 300000,
        entity: {
          libName: "",
        },
      },
      callback: (res) => {
        if (res.code === 200) {
          setList(res?.data?.records || []);
          fetchRequest({
            api: "settingInfo",
            params: {},
            callback: (val) => {
              setEditLoading(false);
              if (val.code === 200) {
                // isWebLib == 1 为网页知识库
                const id = res?.data?.records?.find((item) => item.isWebLib == "1")?.id || "";
                if (val?.data) {
                  if (!val?.data?.defaultStorageLibId) {
                    val.data.defaultStorageLibId = id; // 修正为赋值
                  }
                  setInfo(val.data);
                } else {
                  setInfo({ defaultStorage: false, defaultStorageLibId: id });
                }
              }
            },
          });
        } else {
          message.open({
            type: "error",
            content: "获取失败",
          });
        }
        setEditLoading(false);
      },
    });
  };
  // 网页知识自动采集 确定按钮
  const handleOk = () => {
    setEditLoading(true);
    fetchRequest({
      api: "editSetting",
      params: info,
      callback: (res) => {
        if (res.code == 200) {
          setWebVisible(false);
          browser.storage.local.set({
            settingInfo: res.data,
          });
          message.success("操作成功");
        }
        setEditLoading(false);
      },
    });
  };
  // AI问答
  const AIQA = () => {
    if (checkedData?.length > 5) {
      message.open({
        type: "error",
        content: "最多选择5个知识库或知识",
      });
      return;
    }
    toChat(checkedData);
  };

  const getType = (fileName) => {
    const typeMap = {
      ".pdf": "application/pdf",
      ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      ".xls": "application/vnd.ms-excel",
      ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ".csv": "text/csv",
      ".txt": "text/plain",
      ".ppt": "application/vnd.ms-powerpoint",
      ".doc": "application/msword",
    };
    const type = typeMap[fileName.split(".").pop().toLowerCase()];
    return type;
  };

  // 下载
  const downFile = (data: any) => {
    setPageLoading(true);
    fetchRequest({
      api: "downLoadFile",
      params: {
        id: data.ossId,
      },
      callback: (res) => {
        setLoading(false);
        let type = getType(data.fileName);
        const blob = base64ToBlob(res, type);
        const aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = URL.createObjectURL(blob);
        aLink.setAttribute("download", data.fileName); // 设置下载文件名称
        document.body.appendChild(aLink);
        aLink.click();
        URL.revokeObjectURL(aLink.href); // 清除引用
        document.body.removeChild(aLink);
        message.open({
          type: "success",
          content: "下载成功",
        });
        setPageLoading(false);
      },
    });
  };

  useEffect(() => {
    getUserList();
    getUserInfo().then((res) => {
      if (res) {
        setCurrentUserInfo(res);
      }
    });
  }, []);

  return (
    <>
      <Spin spinning={pageLoading} size="default">
        <Flex className="knowledge-content" vertical>
          <TopTitle
            title={knowHome}
            titleDetail={knowHome === "知识库" ? false : true}
            handleBackPar={handleBack}
            rightSlot={
              knowHome === "知识库" ? (
                <Flex align="center" style={{ marginRight: token.margin }}>
                  <Tag className="title-count" onClick={getSelectData} style={{ marginLeft: "0px", cursor: "pointer" }}>
                    网页知识
                  </Tag>
                  <Popover
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    content={<div style={{ width: 160 }}>开启后系统会自动抓取网页并存入知识库。</div>}
                    placement="bottom"
                    title="网页知识"
                    trigger="hover"
                  >
                    <InfoCircleTwoTone style={{ marginLeft: token.marginXXS, cursor: "pointer" }} />
                  </Popover>
                </Flex>
              ) : (
                <Tag className="title-count">共{dataLength}项</Tag>
              )
            }
          ></TopTitle>
          {/* 知识库列表 */}
          {showPage == "homePage" && (
            <Flex className="knowledge-content-main" vertical>
              <Flex justify="space-between" align="center" gap={token.marginXS}>
                <Segmented
                  size="large"
                  value={alignValue}
                  onChange={(e) => {
                    setKnowHome("知识库");
                    setBreadcrumbItems([]);
                    setNavigationStack([]); // 清空导航栈
                    setClassData([]);
                    setCheckedIds([]); // 清空选中的数据
                    setCheckedData([]); // 清空选中的数据
                    setCurrentClassId("");
                    setAlignValue(e);
                    setIsLearnType(e);
                  }}
                  options={optionsList.map((item) => ({
                    label: item.label,
                    value: item.key,
                  }))}
                />
                <Button
                  icon={<SearchOutlined />}
                  className="search-icon"
                  onClick={() => {
                    setSearchValue("");
                    setSearchData({});
                    // setBreadcrumbItems([]);
                    // setNavigationStack([]); // 清空导航栈
                    setKnowHome("知识库");
                    setCheckedIds([]); // 清空选中的数据
                    setCheckedData([]); // 清空选中的数据
                    setShowPage("searchPage");
                  }}
                />
              </Flex>
              {alignValue == "3" && breadcrumbItems.length > 0 && (
                <Breadcrumb style={{ marginTop: token.margin }} items={breadcrumbItems} />
              )}

              {alignValue == "1" ? (
                <Flex vertical className="card-content-info card-recently">
                  {Object.entries(cardData).some(
                    ([, value]) =>
                      (value?.knowledgeLibBaseList?.length || 0) > 0 || (value?.knowledgeLibDocList?.length || 0) > 0,
                  ) ? (
                    Object.entries(cardData).map(([key, value]) => {
                      const baseList = value?.knowledgeLibBaseList || [];
                      const docList = value?.knowledgeLibDocList || [];
                      const totalLength = baseList.length + docList.length;

                      if (totalLength === 0) return null;

                      const isExpanded = expandedMap[key];
                      const maxToShow = 6;

                      const displayBaseList = isExpanded ? baseList : baseList.slice(0, maxToShow);
                      const remainingSlots = maxToShow - displayBaseList.length;
                      const displayDocList = isExpanded ? docList : docList.slice(0, Math.max(remainingSlots, 0));
                      const isSearchType = typeMap[key];
                      return (
                        <div key={key} style={{ marginBottom: token.marginMD }}>
                          <Flex className="con-tit">
                            {key === "enterpriseQueryVO" && <span>企业</span>}
                            {key === "teamQueryVO" && <span>个人</span>}
                            {key === "projectQueryVO" && <span>项目</span>}
                          </Flex>

                          <Flex
                            wrap="wrap"
                            gap={token.marginXS}
                            style={{ marginBottom: token.marginXS }}
                            className="grid-size"
                          >
                            {displayBaseList.map((item) => (
                              <div
                                key={item.id}
                                onClick={() => {
                                  setCurrentId(item.id);
                                  setCheckedIds([]); // 清空选中的数据
                                  setKnowledgeList([]);
                                  setDataLength(0);
                                  setCheckedData([]); // 清空选中的数据
                                  debounceKnowSearch();
                                  setIsLearnType(isSearchType); // 是哪个下面的知识
                                  // setIsSearch(false); // 从最近模块跳转，不是搜索
                                  isSearch.current = false;
                                  getKnowDetail(item, item?.libName); // 设置面包屑和导航栈
                                  setSearchWebValue("");
                                  setShowPage("knowledgePage"); // 知识库详情
                                }}
                              >
                                <KnowledgeCard
                                  data={item}
                                  knowledgeType={isSearchType}
                                  currentUserInfo={currentUserInfo}
                                  checked={checkedIds.includes(item.id)}
                                  onDelete={handleKnowledgeDelete}
                                  setParentLoading={setPageLoading}
                                  onEdit={handkeEdit}
                                  onShare={handkeShare}
                                  onCommon={(data) => {
                                    if (currentClassId == "0-0") {
                                      getCommonData();
                                    } else {
                                      getRecentList(alignValue);
                                    }
                                  }}
                                  onCheckChange={handleCheckChange}
                                />
                              </div>
                            ))}
                          </Flex>
                          <Flex
                            wrap="wrap"
                            gap={token.marginXS}
                            style={{ marginBottom: token.marginXS }}
                            className="grid-size"
                          >
                            {displayDocList.map((item) => (
                              <div key={item.id}>
                                <LearnCard
                                  data={item}
                                  knowledgeType={isSearchType}
                                  currentUserInfo={currentUserInfo}
                                  checked={checkedIds.includes(item.id)}
                                  onDelete={handleDelete}
                                  onTransfer={handleTransfer}
                                  onCheckChange={handleCheckChange}
                                  ondown={downFile}
                                />
                              </div>
                            ))}
                          </Flex>
                          {!isExpanded && totalLength > maxToShow && (
                            <span
                              style={{
                                cursor: "pointer",
                                fontSize: token.fontSizeSM,
                                color: token.colorInfoText,
                                marginTop: token.marginXS,
                                display: "inline-block",
                              }}
                              onClick={() => toggleExpand(key)}
                            >
                              查看全部（{totalLength}）
                            </span>
                          )}
                        </div>
                      );
                    })
                  ) : (
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={"暂无数据"} />
                  )}
                </Flex>
              ) : (
                <Flex className="card-content-info" gap={token.marginXS}>
                  {alignValue == "4" && ( // 个人库才展示
                    <div>
                      <Flex
                        className="web-page-card"
                        vertical
                        onClick={() => {
                          setCheckedIds([]); // 清空选中的数据
                          setCheckedData([]); // 清空选中的数据
                          setKnowHome("网页知识库");
                          setWebList([]);
                          setDataLength(0);
                          debounceKnowSearch();
                          setCurrentId(webUrlCardData.id);
                          setSearchWebValue("");
                          setShowPage("webUrlPage");
                        }}
                      >
                        <Flex className="top" align="center">
                          <DesktopOutlined className="page-card-icon" />
                          <Flex className="name">{webUrlCardData.name || "网页知识库"}</Flex>
                        </Flex>
                        <Flex className="web-page-bottom" justify="space-between" style={{ marginTop: token.marginXS }}>
                          <Flex className="page-length" align="center">
                            <FileOutlined />
                            <span style={{ marginLeft: "2px", marginRight: token.marginXXS }}>
                              {webUrlCardData.docCount || 0}
                            </span>
                            <SaveOutlined style={{ marginLeft: token.marginXXS }} />
                            <span style={{ marginLeft: "2px" }}>{webUrlCardData?.allFileSize || 0 + "MB"}</span>
                          </Flex>
                        </Flex>
                      </Flex>
                    </div>
                  )}
                  {alignValue == "3" && currentClassId == "" && (
                    <div
                      onClick={() => {
                        setCheckedIds([]); // 清空选中的数据
                        setCheckedData([]); // 清空选中的数据
                        setCurrentClassId(defultCommon.id);
                        getKnowDetail(defultCommon, defultCommon.name);
                      }}
                    >
                      <ClassCard data={defultCommon} />
                    </div>
                  )}
                  {(cardData && cardData.length > 0) || (classData && classData.length > 0) ? (
                    <>
                      {alignValue != "3" || (alignValue == "3" && currentClassId != "") ? ( // 不是企业库的展示
                        <>
                          <Flex className="grid-size" wrap="wrap" gap={token.marginXS} style={{ gridColumn: "1 / -1" }}>
                            {classData?.map((item, index) => (
                              <div
                                key={index}
                                onClick={() => {
                                  setCheckedIds([]); // 清空选中的数据
                                  setCheckedData([]); // 清空选中的数据
                                  setCurrentClassId(item.id);
                                  getKnowDetail(item, item.name);
                                }}
                              >
                                <ClassCard data={item} />
                              </div>
                            ))}
                          </Flex>
                          {cardData?.map((item, index) => (
                            <div
                              key={index}
                              onClick={() => {
                                setCurrentId(item.id);
                                setCheckedIds([]); // 清空选中的数据
                                setKnowledgeList([]);
                                setDataLength(0);
                                setCheckedData([]); // 清空选中的数据
                                debounceKnowSearch();
                                // setIsSearch(false); // 从其他模块跳转，不是搜索
                                isSearch.current = false;
                                getKnowDetail(item, item.libName);
                                setSearchWebValue("");
                                setShowPage("knowledgePage"); // 知识库详情
                              }}
                            >
                              <KnowledgeCard
                                data={item}
                                knowledgeType={isLearnType}
                                checked={checkedIds.includes(item.id)}
                                onDelete={handleKnowledgeDelete}
                                onEdit={handkeEdit}
                                onShare={handkeShare}
                                currentUserInfo={currentUserInfo}
                                setParentLoading={setPageLoading}
                                onCommon={(data) => {
                                  if (currentClassId == "0-0") {
                                    getCommonData();
                                  } else {
                                    getRecentList(alignValue);
                                  }
                                }}
                                onCheckChange={handleCheckChange}
                              />
                            </div>
                          ))}
                        </>
                      ) : (
                        <>
                          {classData?.map((item, index) => (
                            <div
                              key={index}
                              onClick={() => {
                                onClassClick(item);
                              }}
                            >
                              <ClassCard data={item} key={index} />
                            </div>
                          ))}
                        </>
                      )}
                    </>
                  ) : (
                    // 因为个人库有网页知识库所以这里不需要暂无数据, 企业库分类有常用的写死的所以也不需要
                    <>
                      {alignValue !== "4" &&
                        ((alignValue === "3" && currentClassId !== "") || currentClassId === "") && (
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无数据" />
                        )}
                    </>
                  )}
                </Flex>
              )}
              {(alignValue == "2" || alignValue == "4") && (
                <Flex className="btn">
                  <Button block size="large" icon={<PlusOutlined />} className="submit-btn" onClick={createKnowledge}>
                    新建知识库
                  </Button>
                </Flex>
              )}
            </Flex>
          )}
          {/* 列表搜索 */}
          {showPage == "searchPage" && (
            <Flex className="know-search" vertical>
              <Flex className="knowledge-content-input" gap={token.marginXXS}>
                <Input
                  size="large"
                  prefix={<SearchOutlined />}
                  allowClear
                  placeholder="请输入关键字"
                  style={{ width: "100%" }}
                  onChange={inputChange}
                  value={searchValue}
                  onCompositionStart={handleCompositionStart}
                  onCompositionEnd={handleCompositionEnd}
                />

                <Button
                  type="text"
                  size="large"
                  onClick={() => {
                    setSearchValue("");
                    setSearchData({});
                    setShowPage("homePage");
                    // setIsSearch(false);
                    isSearch.current = false;
                    getRecentList(alignValue);
                    if (breadcrumbItems && breadcrumbItems.length > 0) {
                      if (breadcrumbItems[breadcrumbItems.length - 1]?.key !== "root") {
                        const name = breadcrumbItems[breadcrumbItems.length - 1]?.title?.props?.children;
                        setKnowHome(name);
                      } else {
                        setKnowHome("知识库");
                      }
                    } else {
                      setKnowHome("知识库");
                    }
                  }}
                >
                  取消
                </Button>
              </Flex>
              <Flex className="know-search-con" vertical>
                {Object.entries(searchData).some(
                  ([, value]) =>
                    (value?.knowledgeLibBaseList?.length || 0) > 0 || (value?.knowledgeLibDocList?.length || 0) > 0,
                ) ? (
                  Object.entries(searchData).map(([key, value]) => {
                    const baseList = value?.knowledgeLibBaseList || [];
                    const docList = value?.knowledgeLibDocList || [];
                    const totalLength = baseList.length + docList.length;

                    if (totalLength === 0) return null;

                    const isExpanded = expandedMap[key];
                    const maxToShow = 6;

                    const displayBaseList = isExpanded ? baseList : baseList.slice(0, maxToShow);
                    const remainingSlots = maxToShow - displayBaseList.length;
                    const displayDocList = isExpanded ? docList : docList.slice(0, Math.max(remainingSlots, 0));
                    const isSearchType = typeMap[key];
                    return (
                      <div key={key} style={{ marginBottom: token.marginMD }}>
                        <Flex className="con-tit">
                          {key === "enterpriseQueryVO" && <span>企业</span>}
                          {key === "teamQueryVO" && <span>个人</span>}
                          {key === "projectQueryVO" && <span>项目</span>}
                        </Flex>

                        <Flex
                          wrap="wrap"
                          gap={token.marginXS}
                          style={{ marginBottom: token.marginXS }}
                          className="grid-size"
                        >
                          {displayBaseList.map((item) => (
                            <div
                              key={item.id}
                              onClick={() => {
                                setCurrentId(item.id);
                                setCheckedIds([]); // 清空选中的数据
                                setKnowledgeList([]);
                                setDataLength(0);
                                setCheckedData([]); // 清空选中的数据
                                debounceKnowSearch();
                                setIsLearnType(isSearchType); // 哪个库下面的知识
                                // setIsSearch(true);
                                isSearch.current = true;
                                getKnowDetail(item, item?.libName); // 设置面包屑和导航栈
                                setSearchWebValue("");
                                setShowPage("knowledgePage"); // 知识库详情
                              }}
                            >
                              <KnowledgeCard
                                data={item}
                                knowledgeType={isSearchType}
                                currentUserInfo={currentUserInfo}
                                checked={checkedIds.includes(item.id)}
                                onDelete={handleKnowledgeDelete}
                                setParentLoading={setPageLoading}
                                onEdit={handkeEdit}
                                onShare={handkeShare}
                                onCommon={(data) => {
                                  debounceSearch();
                                }}
                                onCheckChange={handleCheckChange}
                              />
                            </div>
                          ))}
                        </Flex>
                        <Flex
                          wrap="wrap"
                          gap={token.marginXS}
                          style={{ marginBottom: token.marginXS }}
                          className="grid-size"
                        >
                          {displayDocList.map((item) => (
                            <div key={item.id}>
                              <LearnCard
                                data={item}
                                knowledgeType={isSearchType}
                                currentUserInfo={currentUserInfo}
                                checked={checkedIds.includes(item.id)}
                                onDelete={handleDelete}
                                onTransfer={handleTransfer}
                                onCheckChange={handleCheckChange}
                                ondown={downFile}
                              />
                            </div>
                          ))}
                        </Flex>
                        {!isExpanded && totalLength > maxToShow && (
                          <span
                            style={{
                              cursor: "pointer",
                              fontSize: token.fontSizeSM,
                              color: token.colorInfoText,
                              marginTop: token.marginXS,
                              display: "inline-block",
                            }}
                            onClick={() => toggleExpand(key)}
                          >
                            查看全部（{totalLength}）
                          </span>
                        )}
                      </div>
                    );
                  })
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={"暂无数据"} />
                )}
              </Flex>
            </Flex>
          )}
          {/* 网页列表*/}
          {showPage == "webUrlPage" && (
            <Flex className="web-knowledge" vertical>
              <Flex className="knowledge-content-input" gap={token.marginXXS}>
                <Input
                  size="large"
                  prefix={<SearchOutlined />}
                  allowClear
                  placeholder="搜索知识"
                  style={{ width: "100%" }}
                  onChange={inputWebChange}
                  value={searchWebValue}
                  onCompositionStart={handleWebStart}
                  onCompositionEnd={handleWebEnd}
                />
                {searchWebValue && (
                  <Button
                    type="text"
                    size="large"
                    onClick={() => {
                      setSearchWebValue("");
                      debounceKnowSearch();
                    }}
                  >
                    取消
                  </Button>
                )}
              </Flex>
              <Flex justify="space-between" align="center">
                <Flex gap={token.marginXXS} className="web-know-tag">
                  {tagsData.map((tag) => (
                    <CheckableTag
                      key={tag.value}
                      style={{
                        backgroundColor: selectedTags === tag.value ? token.colorPrimary : token.colorFillTertiary,
                        color: selectedTags === tag.value ? "#fff" : undefined,
                      }}
                      checked={selectedTags === tag.value}
                      onChange={() => handleChange(tag)}
                    >
                      {tag.label}
                    </CheckableTag>
                  ))}
                </Flex>
                <Flex style={{ color: token.colorTextSecondary, fontSize: token.fontSizeSM }}>
                  已选{checkedIds?.length || 0}个
                </Flex>
              </Flex>
              <Flex className="web-con">
                {webList && webList.length > 0 ? (
                  <Flex className="grid-size" wrap="wrap" gap={token.marginXS}>
                    {webList?.map((item) => (
                      <div key={item.id}>
                        <LearnCard
                          data={item}
                          key={item.id}
                          knowledgeType={isLearnType}
                          currentUserInfo={currentUserInfo}
                          checked={checkedIds.includes(item.id)}
                          onDelete={handleDelete}
                          onTransfer={handleTransfer}
                          onCheckChange={handleCheckChange}
                          ondown={downFile}
                        />
                      </div>
                    ))}
                  </Flex>
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={"暂无数据"} />
                )}
              </Flex>
              <Flex className="btn">
                <Button block size="large" icon={<PlusOutlined />} className="submit-btn" onClick={addInterItem}>
                  添加当前网页
                </Button>
              </Flex>
            </Flex>
          )}
          {/* 知识列表*/}
          {showPage == "knowledgePage" && (
            <Flex className="web-knowledge" vertical>
              <Flex className="knowledge-content-input" gap={token.marginXXS}>
                <Input
                  size="large"
                  prefix={<SearchOutlined />}
                  allowClear
                  placeholder="搜索知识"
                  style={{ width: "100%" }}
                  onChange={inputWebChange}
                  value={searchWebValue}
                  onCompositionStart={handleWebStart}
                  onCompositionEnd={handleWebEnd}
                />
                {searchWebValue && (
                  <Button
                    type="text"
                    size="large"
                    onClick={() => {
                      setSearchWebValue("");
                      debounceKnowSearch();
                    }}
                  >
                    取消
                  </Button>
                )}
              </Flex>
              {alignValue !== "1" && !isSearch.current && <Breadcrumb items={breadcrumbItems} />}
              <Flex className={`page-con ${isLearnType === "2" || isLearnType === "4" ? "page-con-knowledge" : ""}`}>
                {knowledgeList && knowledgeList.length > 0 ? (
                  <Flex className="grid-size" wrap="wrap" gap={token.marginXS}>
                    {knowledgeList?.map((item) => (
                      <div key={item.id}>
                        <LearnCard
                          data={item}
                          key={item.id}
                          knowledgeType={isLearnType}
                          currentUserInfo={currentUserInfo}
                          checked={checkedIds.includes(item.id)}
                          onDelete={handleDelete}
                          onTransfer={handleTransfer}
                          onCheckChange={handleCheckChange}
                          ondown={downFile}
                        />
                      </div>
                    ))}
                  </Flex>
                ) : (
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={"暂无数据"} />
                )}
              </Flex>
              {(isLearnType == "2" || isLearnType == "4") && (
                <Flex className="btn">
                  <Upload {...uploadProps} maxCount={5} customRequest={handleCustomRequest}>
                    <Flex className="btn">
                      <Button block size="large" className="submit-btn" icon={<UploadOutlined />}>
                        上传文档
                      </Button>
                    </Flex>
                  </Upload>
                </Flex>
              )}
            </Flex>
          )}
          {/* 知识库底部操作栏*/}
          {checkedIds && checkedIds.length > 0 && (
            <Flex className="fixed-bottom" gap={token.marginXS}>
              <Flex className="fixed-bottom-operate" vertical align="center">
                <span>{checkedIds?.length || 0}</span>
                <span style={{ marginTop: "6px" }}>已选</span>
              </Flex>
              {alignValue == "4" && (
                <Popconfirm
                  title="确认要删除选中的知识吗？"
                  onConfirm={() => {
                    if (selectType == "2") {
                      handleDelete("", 2);
                    }
                    if (selectType == "1") {
                      handleKnowledgeDelete("", 2);
                    }
                  }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  okText="删除"
                  cancelText="取消"
                >
                  <Flex className="fixed-bottom-operate operate-hover" vertical align="center">
                    <span>
                      <DeleteOutlined />
                    </span>
                    <span>删除</span>
                  </Flex>
                </Popconfirm>
              )}
              {/* 只有是个人库网页库知识才有转移功能 */}
              {(showPage == "webUrlPage" || showPage == "knowledgePage") && alignValue == "4" && (
                <Flex
                  className="fixed-bottom-operate operate-hover"
                  vertical
                  align="center"
                  onClick={() => {
                    setTransferKnowData({});
                    handleTransfer();
                  }}
                >
                  <span>
                    <SelectOutlined />
                  </span>
                  <span>转移</span>
                </Flex>
              )}
              <div className="fixed-bottom-line"></div>
              <Flex className="fixed-bottom-operate operate-hover" vertical align="center" onClick={AIQA}>
                <span>
                  <IconFont type="AIChatOutlined" isGradien={true} width={16} height={16} />
                </span>
                <span>问答</span>
              </Flex>
              <div className="fixed-bottom-line"></div>
              <Flex className="fixed-bottom-close fixed-bottom-operate operate-hover" align="center">
                <Tooltip
                  placement="top"
                  title="点击将清空选中项"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <span
                    onClick={() => {
                      setCheckedIds([]);
                      setCheckedData([]); // 清空选中的数据
                    }}
                  >
                    清空
                  </span>
                </Tooltip>
              </Flex>
            </Flex>
          )}
          {/* 新建知识库 */}
          <Modal
            title={addOredit === "add" ? "新建知识库" : "编辑知识库"}
            centered
            width={"85%"}
            getContainer={getContainer}
            open={modal2Open}
            onCancel={() => {
              setModal2Open(false);
              setEditLoading(false);
              setLoading(false);
              form.resetFields();
            }}
            footer={null}
            okText="确认"
            cancelText="取消"
          >
            <Spin spinning={editLoading} size="default">
              <Form form={form} name="validateOnly" layout="vertical" autoComplete="off" onFinish={handlerSubmit}>
                <Form.Item label="知识库类型" rules={[{ required: false, message: "必填项" }]}>
                  <Select
                    defaultValue="TEXT_LIBRARY"
                    options={[
                      {
                        value: "TEXT_LIBRARY",
                        label: "文本库",
                      },
                    ]}
                  />
                </Form.Item>
                <Form.Item name="libName" label="知识库名称" rules={[{ required: true, message: "必填项" }]}>
                  <Input showCount maxLength={50} placeholder="请输入知识库名称" />
                </Form.Item>
                <Form.Item label="描述" rules={[{ required: false }]} name="libDesc">
                  <TextArea rows={3} showCount maxLength={200} placeholder="请输入描述" />
                </Form.Item>
                {alignValue == "2" && (
                  <>
                    <Divider style={{ marginBottom: "12px", marginTop: "40px" }} />
                    <Form.Item label="分享(协同)" name="userInfo">
                      <Cascader
                        getPopupContainer={(triggerNode) => triggerNode.parentNode}
                        style={{ width: "100%" }}
                        options={userList}
                        onChange={(value) => {
                          if (value.length > 0) {
                            const flatIds = value.map((v) => v[v.length - 1]);
                            const selectedUsers = findLabelsByIds(userList, flatIds);
                            setSelectUser(selectedUsers);
                          } else {
                            setSelectUser([]);
                          }
                        }}
                        multiple
                        placeholder="请选择协同人"
                        maxTagCount="responsive"
                        showSearch={{
                          filter: (inputValue, path) =>
                            path.some((option) => option.realName.toLowerCase().includes(inputValue.toLowerCase())),
                        }}
                        fieldNames={{ label: "realName", value: "userId", children: "children" }}
                      />
                    </Form.Item>
                  </>
                )}
                <Flex justify="flex-end" gap="small">
                  <Button
                    htmlType="button"
                    onClick={() => {
                      setModal2Open(false);
                      setEditLoading(false);
                      setLoading(false);
                      form.resetFields();
                    }}
                    className="setup-model-btn-cancel"
                  >
                    取消
                  </Button>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    保存
                  </Button>
                </Flex>
              </Form>
            </Spin>
          </Modal>
          {/* 转移 */}
          <Modal
            title="转移到"
            open={visible}
            centered
            onCancel={onCancel}
            getContainer={getContainer}
            footer={null}
            width={300}
          >
            <Spin spinning={editLoading} size="default">
              <Form form={transferForm} onFinish={onSubmit} layout="vertical">
                <Form.Item label="个人知识库" name="libId" rules={[{ required: true, message: "请选择知识库" }]}>
                  <Flex>
                    <Select
                      showSearch
                      placeholder="请选择知识库"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                      options={(transferList || []).map((agent) => ({
                        label: agent.libName,
                        value: agent.id,
                      }))}
                      value={transferForm.getFieldValue("libId")}
                      style={{ marginTop: token.marginXS }}
                      onChange={(e) => {
                        transferForm.setFieldsValue({
                          libId: e,
                        });
                      }}
                    />
                  </Flex>
                </Form.Item>
                <Flex justify="end" gap={token.marginXS}>
                  <Col>
                    <Button type="default" onClick={onCancel}>
                      取消
                    </Button>
                  </Col>
                  <Col>
                    <Button type="primary" htmlType="submit" loading={loading}>
                      确定
                    </Button>
                  </Col>
                </Flex>
              </Form>
            </Spin>
          </Modal>
          {/* // 网页知识自动采集 */}
          <Modal
            title="网页知识自动采集"
            open={webVisible}
            onOk={handleOk}
            onCancel={() => {
              setWebVisible(false);
              setEditLoading(false);
            }}
            okText="确定"
            cancelText="取消"
            width={"85%"}
            getContainer={getContainer}
          >
            <Spin spinning={editLoading} size="default">
              <Flex style={{ marginBottom: token.margin }} vertical>
                <Flex gap={token.marginXS} justify="space-between">
                  <span style={{ color: token.colorText }}>开启网页数据自动采集</span>
                  <Switch
                    checked={info.defaultStorage}
                    onChange={(checked) => {
                      setInfo({ ...info, defaultStorage: checked });
                    }}
                  />
                </Flex>
                <Flex style={{ color: token.colorTextDescription, marginTop: token.marginXS }}>
                  登录状态下，开启后系统会自动抓取网页并存入知识库。
                </Flex>
              </Flex>

              <Flex style={{ marginBottom: token.margin }} vertical>
                <label style={{ color: token.colorText }}>保存网页知识到所选知识库</label>
                <Select
                  placeholder="请选择知识库"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  options={(list || []).map((agent) => ({
                    label: agent.libName,
                    value: agent.id,
                  }))}
                  value={info?.defaultStorageLibId}
                  style={{ marginTop: token.marginXS }}
                  onChange={(e) => {
                    const selectedAgent = list.find((item) => item.id === e);
                    setInfo({ ...info, defaultStorageLibId: e, defaultStorageLibName: selectedAgent.libName });
                  }}
                />
                <Button
                  type="link"
                  style={{ marginTop: token.marginXS, width: "70px" }}
                  onClick={() => {
                    window.open(browser.runtime.getURL("/options.html?message=10"), "_blank");
                  }}
                >
                  黑名单
                </Button>
              </Flex>
            </Spin>
          </Modal>
          {/* 分享弹框 */}
          <Modal
            title="分享知识库"
            open={isShareModal}
            onCancel={() => {
              setIsShareModal(false);
              setEditLoading(false);
              setLoading(false);
              shareModalForm.resetFields();
            }}
            centered
            getContainer={getContainer}
            footer={null}
            width={300}
          >
            <Spin spinning={editLoading} size="default">
              <Form form={shareModalForm} onFinish={onShareSubmit} layout="vertical">
                <Form.Item label="分享人" name="userInfo">
                  <Cascader
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    style={{ width: "100%" }}
                    options={userList}
                    onChange={(value) => {
                      if (value.length > 0) {
                        const flatIds = value.map((v) => v[v.length - 1]);
                        const selectedUsers = findLabelsByIds(userList, flatIds);
                        setSelectUser(selectedUsers);
                      } else {
                        setSelectUser([]);
                      }
                    }}
                    multiple
                    placeholder="请选择分享人"
                    maxTagCount="responsive"
                    showSearch={{
                      filter: (inputValue, path) =>
                        path.some((option) => option.realName.toLowerCase().includes(inputValue.toLowerCase())),
                    }}
                    fieldNames={{ label: "realName", value: "userId", children: "children" }}
                  />
                </Form.Item>
                <Flex justify="end" gap={token.marginXS}>
                  <Col>
                    <Button
                      type="default"
                      onClick={() => {
                        setIsShareModal(false);
                        setEditLoading(false);
                        setLoading(false);
                        shareModalForm.resetFields();
                      }}
                    >
                      取消
                    </Button>
                  </Col>
                  <Col>
                    <Button type="primary" htmlType="submit" loading={loading}>
                      保存
                    </Button>
                  </Col>
                </Flex>
              </Form>
            </Spin>
          </Modal>
        </Flex>
      </Spin>
    </>
  );
};

export default Knowledge;
