import "@/assets/styles/content.less";
import "@/assets/styles/highlights.less";
import createSidePanelWrapper from "./sidepanel";
import { v4 as uuidv4 } from "uuid";
import { createNote } from "@/utils/notes";
import { watchUrlChange } from "@/utils/historyRoute";
import { getConfig } from "@/api/common";

function insertEarlyLoading() {
  if (location.hostname !== "www.tiangong.cn") return;

  const tryInsert = () => {
    const head = document.head;
    const body = document.body;

    if (!head || !body) {
      requestAnimationFrame(tryInsert);
      return;
    }

    // 插入遮罩样式
    const style = document.createElement("style");
    style.textContent = `
      html, body {
        visibility: hidden;
      }
      #__plugin-global-loading {
        position: fixed;
        top: 0; left: 0; right: 0; bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        font-size: 20px;
        color: #333;
        z-index: 999999;
        visibility: visible !important;
      }
      .layout-container{
        .c-card.c-card__theme .flex.items-center.gap-x-\\[6px\\] {
          visibility: hidden;
        }
      }
      .flex.flex-col.items-center.w-\\[860px\\]{
        .el-textarea__inner{
          text-indent: 16px !important;
        }
      }
    `;
    head.appendChild(style);

    // 插入加载中 DOM
    const loading = document.createElement("div");
    loading.id = "__plugin-global-loading";
    loading.textContent = "页面加载中...";
    body.appendChild(loading);
  };

  tryInsert();
}

function insertHighlightStyleWhenReady() {
  const tryInsert = () => {
    const head = document.head;
    if (!head) {
      requestAnimationFrame(tryInsert);
      return;
    }

    const style = document.createElement("style");
    style.textContent = `
      .hypothesis-highlight {
        background-color: rgba(156, 230, 255, 0.5);
      }
      .hypothesis-highlight.is-transparent {
        background-color: rgb(208, 218, 121) !important;
        color: inherit !important;
      }
      .hypothesis-highlight-focused {
        background-color: #ffec3d;
      }
    `;
    head.appendChild(style);
  };

  tryInsert();
}

// ✅ 提取公共页面构建逻辑
function finalizePage(parentElement) {
  // 构建 copilot-header
  const copilotHeader = document.createElement("div");
  copilotHeader.className = "copilot-header";

  const mainTitle = document.createElement("h1");
  mainTitle.className = "main-title";
  mainTitle.textContent = "深度检索";
  mainTitle.setAttribute("data-testid", "main-title");

  copilotHeader.appendChild(mainTitle);

  const appElement = document.getElementById("app");
  if (appElement) {
    appElement.innerHTML = "";
    appElement.appendChild(copilotHeader);
    appElement.appendChild(parentElement);
  }

  const style = document.createElement("style");
  style.textContent = `
    .copilot-header {
      width: 100%;
      background: linear-gradient(180deg, #bde1ff66, #e0f2ff00);
      border-radius: .5rem .5rem 0 0;
      padding: var(--ant-margin-md) var(--ant-margin-lg);
      min-height: 72px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    .copilot-header .main-title {
      color: transparent;
      background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
      background-clip: text;
      -webkit-background-clip: text;
      user-select: none;
      font-size: 30px;
      font-weight: bold;
    }
  `;
  document.head.appendChild(style);

  // 显示页面
  document.getElementById("__plugin-global-loading")?.remove();
  document.documentElement.style.visibility = "visible";
  document.body.style.visibility = "visible";
}

// ✅ 工具函数：等待元素出现
function waitForElement(selector, callback, parent = document.body) {
  const element = parent.querySelector(selector);
  if (element) {
    callback(element);
    return;
  }

  const observer = new MutationObserver(() => {
    const el = parent.querySelector(selector);
    if (el) {
      observer.disconnect();
      callback(el);
    }
  });

  observer.observe(parent, { childList: true, subtree: true });
}

// 执行增强逻辑
// 多个icon替换
function waitForElements(selector, callback, root = document, timeout = 10000) {
  const startTime = Date.now();
  const interval = setInterval(() => {
    const elements = root.querySelectorAll(selector);
    if (elements.length > 0) {
      clearInterval(interval);
      callback(elements);
    } else if (Date.now() - startTime > timeout) {
      clearInterval(interval);
    }
  }, 300);
}

export default defineContentScript({
  matches: ["<all_urls>"],
  runAt: "document_start",
  main() {
    insertEarlyLoading(); // 最早插入 loading 遮罩
    insertHighlightStyleWhenReady(); // 插入高亮样式

    // 页面加载完成后
    window.addEventListener("load", () => {
      // 不是目标网站才注入 SidePanel
      createSidePanelWrapper();

      setInterval(() => {
        browser.runtime.sendMessage({ type: "wakeUp" });
      }, 6000000);
    });
    const handleRouteChange = async () => {
      if (location.hostname === "www.tiangong.cn") {
        const cookieData = await getConfig({});
        const cookieInfo = cookieData?.data?.tiangong.cookie;
        const localDate = new Date(cookieInfo?.Expires);
        // 转换为 GMT 格式
        const expires = localDate.toUTCString();
        document.cookie = `k_sso_token=${cookieInfo.k_sso_token}; expires=${expires}; path=/; domain=${cookieInfo.Domain}`;
        if (location.pathname.startsWith("/project/")) {
          waitForElement(".layout-container", (parentElement) => {
            parentElement.setAttribute("style", "height: calc(100% - 72px);");
            // 等待内部元素加载
            waitForElement(
              ".relative.z-\\[21\\].mr-\\[20px\\].h-full",
              (relativeElement) => {
                relativeElement.remove();
              },
              parentElement,
            );

            waitForElement(
              ".absolute.gap-x-2",
              (absoluteElement) => {
                absoluteElement.remove();
              },
              parentElement,
            );

            waitForElement(
              ".footer-left",
              (footerLeft) => {
                footerLeft.innerHTML = "";
              },
              parentElement,
            );
            waitForElements(
              ".c-card.c-card__theme .flex.items-center.gap-x-\\[6px\\]",
              (targetElements) => {
                targetElements.forEach((el) => {
                  // 替换内容
                  el.innerHTML = `
        <img src="https://copilot.sino-bridge.com:85/copilot-web-app/assets/png/logo-Cg4sWdVK.png" alt class="h-6">
        <span class="text-black">AI智能助手</span>
      `;

                  // 替换后再显示
                  el.style.visibility = "visible";
                });
              },
              parentElement,
            );

            finalizePage(parentElement);
          });
        } else {
          waitForElement(".flex.flex-col.items-center.w-\\[860px\\]", (parentElement) => {
            if (!parentElement) {
              console.warn("未找到 .w-[860px] 的父元素");
              return;
            }

            // 修改样式
            Object.assign(parentElement.style, {
              marginLeft: "auto",
              marginRight: "auto",
              marginTop: "100px",
            });

            // 只保留 input-cover
            const keepClassList = ["input-cover"];
            Array.from(parentElement.children).forEach((child) => {
              const shouldKeep = keepClassList.some((cls) => child.classList.contains(cls));
              if (!shouldKeep) child.remove();
            });

            // 设置 textarea placeholder
            const textarea = parentElement.querySelector("textarea.el-textarea__inner");
            if (textarea) {
              textarea.setAttribute("placeholder", "请输入文档的主题和需求，让文档智能体帮你撰写");
              textarea.style.setProperty("textIndent", "16px", "important");
            }

            // 删除 input-cover 内部的 agent-mode-cover
            const agentModeElements = parentElement.querySelectorAll(".input-cover .agent-mode-cover");
            agentModeElements.forEach((el) => el.remove());

            // 清理 footer
            const footerContainer = document.querySelector(".footer-container");
            if (footerContainer) {
              const footerRight = footerContainer.querySelector(".footer-right");
              const disabledBtn = footerRight?.lastElementChild;
              footerContainer.innerHTML = "";
              if (disabledBtn) footerContainer.appendChild(disabledBtn);
              Object.assign(footerContainer.style, {
                display: "flex",
                justifyContent: "end",
              });
            }

            finalizePage(parentElement);
          });
        }
        const observer = new MutationObserver(() => {
          const target = document.querySelector(".loginDialog");
          if (target) {
            // 获取 .show-close 区域
            const closeArea = target.querySelector(".show-close");
            if (closeArea) {
              // 查找子孙节点中的 img 元素
              const img = closeArea.querySelector("img");
              if (img) {
                img.src = "https://copilot.sino-bridge.com:85/copilot-web-app/assets/png/logo-Cg4sWdVK.png";
              } else {
                console.warn(".show-close 内未找到 img");
              }
            } else {
              console.warn("未找到 .show-close");
            }

            // 查找并替换 loginModal 下的 h3 标签内容
            const h3 = target.querySelector("h3");
            if (h3) {
              h3.textContent = "AI智能助手";
            } else {
              console.warn(".loginModal 下未找到 h3");
            }
            target.style.setProperty("display", "block", "important");
            location.reload();
            observer.disconnect(); // 停止监听
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });
      }
    };
    // 页面dom加载完毕后
    document.addEventListener("DOMContentLoaded", () => {
      setTimeout(() => {
        handleRouteChange();
      }, 500);
    });
    // 监听路由变化
    watchUrlChange(async (url) => {
      console.log(url, 344);
      insertEarlyLoading();

      if (location.hostname === "www.tiangong.cn") {
        const pathname = location.pathname; // "/project/xxx" 或 "/"
        const searchParams = new URLSearchParams(location.search);

        const isHomePage = pathname === "/";
        const isProjectPage = pathname.startsWith("/project/");
        const hasFromHomeQuery = searchParams.get("from") === "home_query";

        if (isHomePage || (isProjectPage && hasFromHomeQuery)) {
          setTimeout(() => {
            location.reload();
          }, 3000);
        }
      }
    });

    // 右键记录便签位置
    let noteMouseX: number;
    let noteMouseY: number;
    document.addEventListener("contextmenu", (event) => {
      noteMouseX = event.clientX;
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      noteMouseY = scrollTop + event.clientY;
    });

    // 设置便签 key
    sessionStorage.setItem("sino-tap-key", uuidv4());

    // 接收消息
    /** 注册高亮消息监听时间 */
    browser.runtime.onMessage.addListener(async (message) => {
      switch (message.type) {
        case "createNote":
          createNote(message, noteMouseX, noteMouseY);
          break;
        default:
          break;
      }
    });
  },
});
