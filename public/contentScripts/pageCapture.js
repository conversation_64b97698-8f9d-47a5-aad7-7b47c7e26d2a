// pageCapture.js - 内容脚本，用于在页面上下文中执行截图逻辑并生成 PNG 图片

(function () {
  // 检查是否已经注入过脚本
  if (window.__captureScriptInjected) {
    return;
  }
  window.__captureScriptInjected = true;

  // 先设置消息监听
  window.addEventListener("message", async (e) => {
    if (e.data && e.data.type === "triggerCapture") {
      try {
        // 获取网页实际宽度和高度
        const pageWidth = Math.max(
          document.body.scrollWidth,
          document.documentElement.scrollWidth,
          document.body.offsetWidth,
          document.documentElement.offsetWidth,
          document.body.clientWidth,
          document.documentElement.clientWidth
        );

        const pageHeight = Math.max(
          document.body.scrollHeight,
          document.documentElement.scrollHeight,
          document.body.offsetHeight,
          document.documentElement.offsetHeight,
          document.body.clientHeight,
          document.documentElement.clientHeight
        );

        // 设置 html2canvas 的 scale（放大渲染，防止模糊）
        const scale = 2;

        // 为每个图片元素设置 crossOrigin 属性
        setCrossOriginForImages();

        // 等待图片资源完全加载
        await waitForImagesLoaded();

        // 设置 html2canvas 配置
        const opt = {
          scale: scale,
          scrollY: 0,
          useCORS: true,
          allowTaint: false, // 改为 false
          foreignObjectRendering: false, // 改为 false，提高兼容性
          width: pageWidth,
          height: pageHeight,
          windowWidth: pageWidth,
          windowHeight: pageHeight,
          x: 0,
          y: 0,
          logging: true, // 添加日志输出，方便调试
          imageTimeout: 0, // 禁用图片加载超时
          onclone: function(clonedDoc) {
            // 在克隆的文档中也设置图片的 crossOrigin
            const clonedImages = clonedDoc.querySelectorAll('img');
            clonedImages.forEach(img => {
              img.crossOrigin = 'anonymous';
            });
          }
        };

        // 使用 html2canvas 生成 canvas
        const canvas = await html2canvas(document.body, opt);

        // 将 canvas 转换为 base64 编码的 PNG 图片
        const pngDataUrl = canvas.toDataURL('image/png');

        // 发送 PNG 数据给 background 或其他部分
        console.log("截图完成，发送 PNG:", pngDataUrl.length);
        chrome.runtime.sendMessage({
          type: "captureResult",
          result: pngDataUrl,
        });
      } catch (err) {
        chrome.runtime.sendMessage({
          type: "captureResult",
          error: err.message,
        });
      }
    }
  });

  // 然后加载 html2canvas 脚本
  const script = document.createElement("script");
  script.src = chrome.runtime.getURL("contentScripts/html2canvas.js");
  document.head.appendChild(script);

  // 为每个图片元素设置 crossOrigin 属性
  function setCrossOriginForImages() {
    const images = document.querySelectorAll('img');
    images.forEach((img) => {
      if (!img.crossOrigin) {
        // 保存原始图片地址
        const originalSrc = img.src;
        // 先设置 crossOrigin 属性
        img.crossOrigin = 'anonymous';
        // 如果图片已经加载完成，需要重新加载以应用 crossOrigin
        if (img.complete) {
          img.src = originalSrc + (originalSrc.includes('?') ? '&' : '?') + 'timestamp=' + new Date().getTime();
        }
      }
    });
  }

  // 等待图片资源完全加载
  function waitForImagesLoaded() {
    return new Promise((resolve) => {
      const images = document.querySelectorAll('img');
      let loadedCount = 0;

      function checkImages() {
        if (loadedCount === images.length) {
          resolve();
        }
      }

      images.forEach((img) => {
        if (img.complete) {
          loadedCount++;
          checkImages();
        } else {
          img.addEventListener('load', () => {
            loadedCount++;
            checkImages();
          });
          img.addEventListener('error', () => {
            loadedCount++;
            checkImages();
          });
        }
      });

      // 如果没有图片，直接resolve
      if (images.length === 0) {
        resolve();
      }
    });
  }
})();

